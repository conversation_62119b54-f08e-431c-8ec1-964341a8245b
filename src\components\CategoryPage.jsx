import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { ArrowRight, Filter, Search, Star, Clock, Heart } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'

const CategoryPage = () => {
  const { categoryId } = useParams()
  const navigate = useNavigate()
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('popular')
  const [favorites, setFavorites] = useState(new Set())

  // Mock data for category
  const categoryData = {
    1: { name: 'برجر', nameEn: 'Burgers', icon: '🍔' },
    2: { name: 'بيتزا', nameEn: 'Pizza', icon: '🍕' },
    3: { name: 'أكل عربي', nameEn: 'Arabic Food', icon: '🥙' },
    4: { name: 'صحي', nameEn: 'Healthy', icon: '🥗' },
    5: { name: 'حلويات', nameEn: 'Desserts', icon: '🍰' },
    6: { name: 'مشروبات', nameEn: 'Drinks', icon: '🥤' }
  }

  const category = categoryData[categoryId] || categoryData[1]

  const products = [
    {
      id: 1,
      name: 'برجر كلاسيك',
      description: 'برجر لحم طازج مع الخضار والصوص الخاص',
      image: '/src/assets/images/products/burger1.jpg',
      price: 25,
      originalPrice: 30,
      rating: 4.8,
      reviews: 156,
      prepTime: '15-20 دقيقة',
      isPopular: true,
      hasDiscount: true,
      restaurant: 'برجر هاوس'
    },
    {
      id: 2,
      name: 'برجر دبل تشيز',
      description: 'برجر مضاعف مع جبنة شيدر ولحم مشوي',
      image: '/src/assets/images/products/burger2.jpg',
      price: 35,
      rating: 4.9,
      reviews: 203,
      prepTime: '20-25 دقيقة',
      isPopular: false,
      hasDiscount: false,
      restaurant: 'برجر هاوس'
    },
    {
      id: 3,
      name: 'برجر نباتي',
      description: 'برجر نباتي صحي مع الخضار الطازجة',
      image: '/src/assets/images/products/burger1.jpg',
      price: 22,
      rating: 4.6,
      reviews: 89,
      prepTime: '10-15 دقيقة',
      isPopular: false,
      hasDiscount: false,
      restaurant: 'جرين برجر'
    },
    {
      id: 4,
      name: 'برجر سبايسي',
      description: 'برجر حار مع الفلفل الحار والصوص الحار',
      image: '/src/assets/images/products/burger2.jpg',
      price: 28,
      originalPrice: 32,
      rating: 4.7,
      reviews: 124,
      prepTime: '15-20 دقيقة',
      isPopular: true,
      hasDiscount: true,
      restaurant: 'فاير برجر'
    }
  ]

  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites)
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId)
    } else {
      newFavorites.add(productId)
    }
    setFavorites(newFavorites)
  }

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price
      case 'price-high':
        return b.price - a.price
      case 'rating':
        return b.rating - a.rating
      case 'popular':
      default:
        return b.isPopular - a.isPopular || b.rating - a.rating
    }
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center justify-between mb-3">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate(-1)}
            >
              <ArrowRight className="h-5 w-5" />
            </Button>
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className="text-2xl">{category.icon}</span>
              <h1 className="text-lg font-bold">{category.name}</h1>
            </div>
            <Button variant="ghost" size="sm">
              <Filter className="h-5 w-5" />
            </Button>
          </div>

          {/* Search Bar */}
          <div className="relative mb-3">
            <Search className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder={`ابحث في ${category.name}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-10 text-right"
            />
          </div>

          {/* Sort Options */}
          <div className="flex space-x-2 space-x-reverse overflow-x-auto pb-2">
            {[
              { key: 'popular', label: 'الأكثر طلباً' },
              { key: 'rating', label: 'الأعلى تقييماً' },
              { key: 'price-low', label: 'الأرخص' },
              { key: 'price-high', label: 'الأغلى' }
            ].map((option) => (
              <Button
                key={option.key}
                variant={sortBy === option.key ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy(option.key)}
                className={`whitespace-nowrap ${
                  sortBy === option.key 
                    ? 'bg-orange-500 hover:bg-orange-600' 
                    : 'border-gray-300'
                }`}
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-md mx-auto px-4 pb-20">
        {/* Results Count */}
        <div className="py-3">
          <p className="text-sm text-gray-600">
            {sortedProducts.length} منتج متاح
          </p>
        </div>

        {/* Products Grid */}
        <div className="space-y-4">
          {sortedProducts.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              className="bg-white rounded-xl shadow-sm overflow-hidden"
            >
              <div className="flex">
                <div className="relative">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-28 h-28 object-cover"
                  />
                  {product.hasDiscount && (
                    <Badge className="absolute top-2 left-2 bg-red-500 text-white text-xs">
                      خصم
                    </Badge>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleFavorite(product.id)}
                    className="absolute top-2 right-2 p-1 bg-white/80 hover:bg-white"
                  >
                    <Heart 
                      className={`h-4 w-4 ${
                        favorites.has(product.id) 
                          ? 'text-red-500 fill-current' 
                          : 'text-gray-400'
                      }`} 
                    />
                  </Button>
                </div>
                
                <div className="flex-1 p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 space-x-reverse mb-1">
                        <h3 className="font-semibold text-gray-800">{product.name}</h3>
                        {product.isPopular && (
                          <Badge className="bg-orange-100 text-orange-800 text-xs">
                            الأكثر طلباً
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {product.description}
                      </p>
                      <p className="text-xs text-gray-500 mb-2">{product.restaurant}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium mr-1">{product.rating}</span>
                        <span className="text-xs text-gray-500">({product.reviews})</span>
                      </div>
                      <div className="flex items-center text-xs text-gray-500">
                        <Clock className="h-3 w-3 ml-1" />
                        {product.prepTime}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-3">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className="font-bold text-orange-600">{product.price} ج.م</span>
                      {product.originalPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          {product.originalPrice} ج.م
                        </span>
                      )}
                    </div>
                    <Button 
                      size="sm" 
                      className="bg-orange-500 hover:bg-orange-600"
                      onClick={() => navigate(`/product/${product.id}`)}
                    >
                      أضف
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {sortedProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              لا توجد نتائج
            </h3>
            <p className="text-gray-600">
              جرب البحث بكلمات مختلفة
            </p>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="max-w-md mx-auto px-4 py-2">
          <div className="flex items-center justify-around">
            <Button 
              variant="ghost" 
              className="flex flex-col items-center py-2 text-gray-600"
              onClick={() => navigate('/home')}
            >
              <div className="w-6 h-6 mb-1">🏠</div>
              <span className="text-xs">الرئيسية</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center py-2 text-orange-600">
              <div className="w-6 h-6 mb-1">🔍</div>
              <span className="text-xs">البحث</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center py-2 text-gray-600">
              <div className="w-6 h-6 mb-1">📋</div>
              <span className="text-xs">طلباتي</span>
            </Button>
            <Button 
              variant="ghost" 
              className="flex flex-col items-center py-2 text-gray-600"
              onClick={() => navigate('/profile')}
            >
              <div className="w-6 h-6 mb-1">👤</div>
              <span className="text-xs">الملف الشخصي</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CategoryPage

