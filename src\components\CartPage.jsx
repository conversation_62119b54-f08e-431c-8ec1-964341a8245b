import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { ArrowRight, Plus, Minus, Trash2, MapPin, Clock, Tag, CreditCard } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

const CartPage = ({ user }) => {
  const navigate = useNavigate()
  const [couponCode, setCouponCode] = useState('')
  const [appliedCoupon, setAppliedCoupon] = useState(null)
  const [deliveryTime, setDeliveryTime] = useState('now')
  
  // Mock cart data
  const [cartItems, setCartItems] = useState([
    {
      id: 1,
      productId: 1,
      name: 'برجر كلاسيك',
      description: 'برجر لحم طازج مع الخضار',
      image: '/src/assets/images/products/burger1.jpg',
      price: 25,
      quantity: 2,
      size: 'متوسط',
      addons: ['جبنة إضافية', 'بطاطس مقلية'],
      restaurant: 'برجر هاوس'
    },
    {
      id: 2,
      productId: 2,
      name: 'بيتزا مارجريتا',
      description: 'بيتزا كلاسيكية بالجبن والطماطم',
      image: '/src/assets/images/products/pizza1.jpg',
      price: 35,
      quantity: 1,
      size: 'كبير',
      addons: ['مشروم'],
      restaurant: 'بيتزا هت'
    }
  ])

  const deliveryFee = 10
  const serviceFee = 5
  const taxRate = 0.14

  const updateQuantity = (itemId, newQuantity) => {
    if (newQuantity <= 0) {
      removeItem(itemId)
      return
    }
    
    setCartItems(items =>
      items.map(item =>
        item.id === itemId ? { ...item, quantity: newQuantity } : item
      )
    )
  }

  const removeItem = (itemId) => {
    setCartItems(items => items.filter(item => item.id !== itemId))
  }

  const applyCoupon = () => {
    // Mock coupon validation
    if (couponCode.toLowerCase() === 'save10') {
      setAppliedCoupon({
        code: 'SAVE10',
        discount: 10,
        type: 'percentage'
      })
      setCouponCode('')
    } else if (couponCode.toLowerCase() === 'free5') {
      setAppliedCoupon({
        code: 'FREE5',
        discount: 5,
        type: 'fixed'
      })
      setCouponCode('')
    } else {
      // Show error message
      alert('كود الخصم غير صحيح')
    }
  }

  const removeCoupon = () => {
    setAppliedCoupon(null)
  }

  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const calculateDiscount = () => {
    if (!appliedCoupon) return 0
    
    const subtotal = calculateSubtotal()
    if (appliedCoupon.type === 'percentage') {
      return (subtotal * appliedCoupon.discount) / 100
    } else {
      return appliedCoupon.discount
    }
  }

  const calculateTax = () => {
    const subtotal = calculateSubtotal()
    const discount = calculateDiscount()
    return (subtotal - discount + deliveryFee + serviceFee) * taxRate
  }

  const calculateTotal = () => {
    const subtotal = calculateSubtotal()
    const discount = calculateDiscount()
    const tax = calculateTax()
    return subtotal - discount + deliveryFee + serviceFee + tax
  }

  const proceedToCheckout = () => {
    if (cartItems.length === 0) return
    
    // Navigate to checkout or order confirmation
    navigate('/checkout', {
      state: {
        items: cartItems,
        subtotal: calculateSubtotal(),
        discount: calculateDiscount(),
        deliveryFee,
        serviceFee,
        tax: calculateTax(),
        total: calculateTotal(),
        coupon: appliedCoupon,
        deliveryTime
      }
    })
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-md mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate(-1)}
              >
                <ArrowRight className="h-5 w-5" />
              </Button>
              <h1 className="text-lg font-bold">سلة التسوق</h1>
              <div></div>
            </div>
          </div>
        </div>
        
        {/* Empty State */}
        <div className="max-w-md mx-auto px-4 py-20 text-center">
          <div className="text-8xl mb-6">🛒</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">سلة التسوق فارغة</h2>
          <p className="text-gray-600 mb-6">أضف بعض المنتجات اللذيذة إلى سلتك</p>
          <Button 
            onClick={() => navigate('/home')}
            className="bg-orange-500 hover:bg-orange-600"
          >
            تصفح المنتجات
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate(-1)}
            >
              <ArrowRight className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-bold">سلة التسوق ({cartItems.length})</h1>
            <div></div>
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto px-4 pb-32">
        {/* Delivery Info */}
        <Card className="mt-4">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 text-orange-500 ml-2" />
                <div>
                  <p className="font-medium">التوصيل إلى</p>
                  <p className="text-sm text-gray-600">المعادي، القاهرة</p>
                </div>
              </div>
              <Button variant="ghost" size="sm" className="text-orange-600">
                تغيير
              </Button>
            </div>
            
            <Separator className="my-3" />
            
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-orange-500 ml-2" />
                <div>
                  <p className="font-medium">وقت التوصيل</p>
                  <div className="flex space-x-2 space-x-reverse mt-1">
                    <Button
                      variant={deliveryTime === 'now' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setDeliveryTime('now')}
                      className={deliveryTime === 'now' ? 'bg-orange-500 hover:bg-orange-600' : ''}
                    >
                      الآن
                    </Button>
                    <Button
                      variant={deliveryTime === 'later' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setDeliveryTime('later')}
                      className={deliveryTime === 'later' ? 'bg-orange-500 hover:bg-orange-600' : ''}
                    >
                      لاحقاً
                    </Button>
                  </div>
                </div>
              </div>
              <p className="text-sm text-gray-600">25-35 دقيقة</p>
            </div>
          </CardContent>
        </Card>

        {/* Cart Items */}
        <Card className="mt-4">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-800 mb-4">طلبك</h3>
            <div className="space-y-4">
              {cartItems.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start space-x-3 space-x-reverse"
                >
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-16 h-16 object-cover rounded-lg"
                  />
                  
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-1">
                      <div>
                        <h4 className="font-medium text-gray-800">{item.name}</h4>
                        <p className="text-sm text-gray-600">{item.restaurant}</p>
                        <p className="text-xs text-gray-500">
                          {item.size} • {item.addons.join(', ')}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(item.id)}
                        className="text-red-500 hover:text-red-600 p-1"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="h-8 w-8 p-0"
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="font-medium px-2">{item.quantity}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                      <p className="font-semibold text-orange-600">
                        {item.price * item.quantity} ج.م
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Coupon */}
        <Card className="mt-4">
          <CardContent className="p-4">
            <div className="flex items-center mb-3">
              <Tag className="h-5 w-5 text-orange-500 ml-2" />
              <h3 className="font-semibold text-gray-800">كود الخصم</h3>
            </div>
            
            {appliedCoupon ? (
              <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center">
                  <Badge className="bg-green-500 text-white ml-2">{appliedCoupon.code}</Badge>
                  <span className="text-sm text-green-700">
                    خصم {appliedCoupon.type === 'percentage' ? `${appliedCoupon.discount}%` : `${appliedCoupon.discount} ج.م`}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={removeCoupon}
                  className="text-red-500 hover:text-red-600"
                >
                  إزالة
                </Button>
              </div>
            ) : (
              <div className="flex space-x-2 space-x-reverse">
                <Input
                  placeholder="أدخل كود الخصم"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  className="text-right"
                />
                <Button
                  onClick={applyCoupon}
                  disabled={!couponCode.trim()}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  تطبيق
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card className="mt-4">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-800 mb-4">ملخص الطلب</h3>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">المجموع الفرعي</span>
                <span>{calculateSubtotal().toFixed(2)} ج.م</span>
              </div>
              
              {appliedCoupon && (
                <div className="flex justify-between text-green-600">
                  <span>الخصم ({appliedCoupon.code})</span>
                  <span>-{calculateDiscount().toFixed(2)} ج.م</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span className="text-gray-600">رسوم التوصيل</span>
                <span>{deliveryFee} ج.م</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">رسوم الخدمة</span>
                <span>{serviceFee} ج.م</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">الضرائب</span>
                <span>{calculateTax().toFixed(2)} ج.م</span>
              </div>
              
              <Separator className="my-3" />
              
              <div className="flex justify-between text-lg font-bold">
                <span>الإجمالي</span>
                <span className="text-orange-600">{calculateTotal().toFixed(2)} ج.م</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Action Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الإجمالي</p>
              <p className="text-xl font-bold text-orange-600">
                {calculateTotal().toFixed(2)} ج.م
              </p>
            </div>
            
            <Button 
              onClick={proceedToCheckout}
              className="bg-orange-500 hover:bg-orange-600 flex items-center space-x-2 space-x-reverse px-8"
            >
              <CreditCard className="h-4 w-4" />
              <span>متابعة الدفع</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CartPage

