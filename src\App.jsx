import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { useState } from 'react'
import SplashScreen from './components/SplashScreen'
import LoginPage from './components/LoginPage'
import HomePage from './components/HomePage'
import CategoryPage from './components/CategoryPage'
import ProductPage from './components/ProductPage'
import CartPage from './components/CartPage'
import OrderTrackingPage from './components/OrderTrackingPage'
import OrderHistoryPage from './components/OrderHistoryPage'
import ProfilePage from './components/ProfilePage'

function App() {
  const [user, setUser] = useState(null)

  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<SplashScreen />} />
          <Route path="/login" element={<LoginPage setUser={setUser} />} />
          <Route path="/home" element={<HomePage user={user} />} />
          <Route path="/category/:categoryId" element={<CategoryPage />} />
          <Route path="/product/:productId" element={<ProductPage />} />
          <Route path="/cart" element={<CartPage user={user} />} />
          <Route path="/order/:orderId" element={<OrderTrackingPage />} />
          <Route path="/orders" element={<OrderHistoryPage user={user} />} />
          <Route path="/profile" element={<ProfilePage user={user} setUser={setUser} />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App

