import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { <PERSON>R<PERSON>, Clock, CheckCircle, XCircle, RotateCcw, Star, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

const OrderHistoryPage = ({ user }) => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('all')

  // Mock orders data
  const orders = [
    {
      id: 'ORD-001',
      date: '2024-01-15',
      time: '14:30',
      status: 'delivered',
      total: 85.50,
      items: [
        { name: 'برجر كلاسيك', quantity: 2, price: 25 },
        { name: 'بيتزا مارجريتا', quantity: 1, price: 35 }
      ],
      restaurant: 'برجر هاوس',
      deliveryTime: '25 دقيقة',
      rating: 5,
      canReorder: true,
      canRate: false
    },
    {
      id: 'ORD-002',
      date: '2024-01-12',
      time: '19:45',
      status: 'delivered',
      total: 42.00,
      items: [
        { name: 'سلطة سيزر', quantity: 1, price: 22 },
        { name: 'عصير برتقال', quantity: 2, price: 10 }
      ],
      restaurant: 'صحي وطازج',
      deliveryTime: '20 دقيقة',
      rating: 4,
      canReorder: true,
      canRate: false
    },
    {
      id: 'ORD-003',
      date: '2024-01-10',
      time: '12:15',
      status: 'cancelled',
      total: 65.00,
      items: [
        { name: 'بيتزا بيبروني', quantity: 1, price: 45 },
        { name: 'مشروب غازي', quantity: 2, price: 10 }
      ],
      restaurant: 'بيتزا هت',
      deliveryTime: '30 دقيقة',
      rating: null,
      canReorder: true,
      canRate: false,
      cancelReason: 'غير متوفر'
    },
    {
      id: 'ORD-004',
      date: '2024-01-08',
      time: '16:20',
      status: 'in_progress',
      total: 38.50,
      items: [
        { name: 'شاورما دجاج', quantity: 1, price: 18 },
        { name: 'بطاطس مقلية', quantity: 1, price: 12 },
        { name: 'مشروب', quantity: 1, price: 8.50 }
      ],
      restaurant: 'شاورما الملك',
      deliveryTime: '15 دقيقة',
      rating: null,
      canReorder: false,
      canRate: false,
      estimatedTime: '10 دقائق متبقية'
    }
  ]

  const getStatusInfo = (status) => {
    switch (status) {
      case 'delivered':
        return {
          label: 'تم التوصيل',
          color: 'bg-green-500',
          icon: CheckCircle,
          textColor: 'text-green-600'
        }
      case 'in_progress':
        return {
          label: 'جاري التحضير',
          color: 'bg-orange-500',
          icon: Clock,
          textColor: 'text-orange-600'
        }
      case 'cancelled':
        return {
          label: 'ملغي',
          color: 'bg-red-500',
          icon: XCircle,
          textColor: 'text-red-600'
        }
      default:
        return {
          label: 'غير معروف',
          color: 'bg-gray-500',
          icon: Clock,
          textColor: 'text-gray-600'
        }
    }
  }

  const filterOrders = (status) => {
    if (status === 'all') return orders
    return orders.filter(order => order.status === status)
  }

  const reorderItems = (order) => {
    // Add items to cart and navigate
    console.log('Reordering:', order.items)
    navigate('/cart')
  }

  const trackOrder = (orderId) => {
    navigate(`/order/${orderId}`)
  }

  const rateOrder = (orderId) => {
    // Open rating modal or navigate to rating page
    console.log('Rating order:', orderId)
  }

  const filteredOrders = filterOrders(activeTab)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate(-1)}
            >
              <ArrowRight className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-bold">طلباتي</h1>
            <div></div>
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto px-4 pb-20">
        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">الكل</TabsTrigger>
            <TabsTrigger value="in_progress">جاري</TabsTrigger>
            <TabsTrigger value="delivered">مكتمل</TabsTrigger>
            <TabsTrigger value="cancelled">ملغي</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-4">
            {filteredOrders.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📋</div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  لا توجد طلبات
                </h3>
                <p className="text-gray-600 mb-4">
                  لم تقم بأي طلبات بعد
                </p>
                <Button 
                  onClick={() => navigate('/home')}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  تصفح المنتجات
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredOrders.map((order, index) => {
                  const statusInfo = getStatusInfo(order.status)
                  const StatusIcon = statusInfo.icon

                  return (
                    <motion.div
                      key={order.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card className="overflow-hidden">
                        <CardContent className="p-4">
                          {/* Order Header */}
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h3 className="font-semibold text-gray-800">#{order.id}</h3>
                              <p className="text-sm text-gray-600">
                                {order.date} • {order.time}
                              </p>
                            </div>
                            <Badge className={`${statusInfo.color} text-white`}>
                              <StatusIcon className="h-3 w-3 ml-1" />
                              {statusInfo.label}
                            </Badge>
                          </div>

                          {/* Restaurant Info */}
                          <div className="mb-3">
                            <p className="font-medium text-gray-800">{order.restaurant}</p>
                            <p className="text-sm text-gray-600">
                              {order.items.length} منتج • {order.deliveryTime}
                            </p>
                          </div>

                          {/* Order Items */}
                          <div className="mb-3">
                            {order.items.map((item, idx) => (
                              <div key={idx} className="flex justify-between text-sm mb-1">
                                <span className="text-gray-700">
                                  {item.quantity}x {item.name}
                                </span>
                                <span className="text-gray-600">{item.price} ج.م</span>
                              </div>
                            ))}
                          </div>

                          {/* Total */}
                          <div className="flex justify-between items-center mb-3 pt-2 border-t border-gray-100">
                            <span className="font-semibold text-gray-800">الإجمالي</span>
                            <span className="font-bold text-orange-600">{order.total} ج.م</span>
                          </div>

                          {/* Status Specific Info */}
                          {order.status === 'in_progress' && order.estimatedTime && (
                            <div className="mb-3 p-2 bg-orange-50 rounded-lg">
                              <p className="text-sm text-orange-700 text-center">
                                <Clock className="h-4 w-4 inline ml-1" />
                                {order.estimatedTime}
                              </p>
                            </div>
                          )}

                          {order.status === 'cancelled' && order.cancelReason && (
                            <div className="mb-3 p-2 bg-red-50 rounded-lg">
                              <p className="text-sm text-red-700 text-center">
                                سبب الإلغاء: {order.cancelReason}
                              </p>
                            </div>
                          )}

                          {/* Rating */}
                          {order.rating && (
                            <div className="mb-3 flex items-center justify-center">
                              <span className="text-sm text-gray-600 ml-2">تقييمك:</span>
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-4 w-4 ${
                                      i < order.rating
                                        ? 'text-yellow-400 fill-current'
                                        : 'text-gray-300'
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Action Buttons */}
                          <div className="flex space-x-2 space-x-reverse">
                            {order.status === 'in_progress' && (
                              <Button
                                onClick={() => trackOrder(order.id)}
                                className="flex-1 bg-orange-500 hover:bg-orange-600"
                              >
                                تتبع الطلب
                              </Button>
                            )}

                            {order.canReorder && (
                              <Button
                                onClick={() => reorderItems(order)}
                                variant="outline"
                                className="flex-1"
                              >
                                <RotateCcw className="h-4 w-4 ml-1" />
                                اطلب مرة أخرى
                              </Button>
                            )}

                            {order.canRate && order.status === 'delivered' && (
                              <Button
                                onClick={() => rateOrder(order.id)}
                                variant="outline"
                                className="flex-1"
                              >
                                <Star className="h-4 w-4 ml-1" />
                                قيم الطلب
                              </Button>
                            )}

                            {order.status === 'delivered' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-gray-600"
                              >
                                <MessageCircle className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="max-w-md mx-auto px-4 py-2">
          <div className="flex items-center justify-around">
            <Button 
              variant="ghost" 
              className="flex flex-col items-center py-2 text-gray-600"
              onClick={() => navigate('/home')}
            >
              <div className="w-6 h-6 mb-1">🏠</div>
              <span className="text-xs">الرئيسية</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center py-2 text-gray-600">
              <div className="w-6 h-6 mb-1">🔍</div>
              <span className="text-xs">البحث</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center py-2 text-orange-600">
              <div className="w-6 h-6 mb-1">📋</div>
              <span className="text-xs">طلباتي</span>
            </Button>
            <Button 
              variant="ghost" 
              className="flex flex-col items-center py-2 text-gray-600"
              onClick={() => navigate('/profile')}
            >
              <div className="w-6 h-6 mb-1">👤</div>
              <span className="text-xs">الملف الشخصي</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OrderHistoryPage

