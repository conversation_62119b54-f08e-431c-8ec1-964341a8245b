import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'

const SplashScreen = () => {
  const [currentQuote, setCurrentQuote] = useState(0)
  
  const quotes = [
    "مزاجك على كيفك",
    "طعامك المفضل في دقائق",
    "اكتشف نكهات جديدة",
    "وجبتك تصل إليك"
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote((prev) => (prev + 1) % quotes.length)
    }, 800)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-orange-500 to-yellow-400 flex flex-col items-center justify-center relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <img 
          src="/src/assets/images/splash-bg.png" 
          alt="Background" 
          className="w-full h-full object-cover"
        />
      </div>
      
      {/* Animated Logo */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ 
          duration: 1,
          ease: "easeOut",
          delay: 0.2
        }}
        className="relative z-10 mb-8"
      >
        <img 
          src="/src/assets/images/logo.png" 
          alt="FoodMood Logo" 
          className="w-32 h-32 mx-auto drop-shadow-2xl"
        />
      </motion.div>

      {/* App Name */}
      <motion.h1
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ 
          duration: 0.8,
          ease: "easeOut",
          delay: 0.8
        }}
        className="text-4xl font-bold text-white mb-4 text-center drop-shadow-lg"
      >
        FoodMood
      </motion.h1>

      {/* Animated Quote */}
      <motion.div
        key={currentQuote}
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: -20, opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="text-xl text-white/90 text-center font-medium mb-12 px-8"
      >
        {quotes[currentQuote]}
      </motion.div>

      {/* Loading Animation */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.5 }}
        className="flex space-x-2"
      >
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: i * 0.2
            }}
            className="w-3 h-3 bg-white rounded-full"
          />
        ))}
      </motion.div>

      {/* Floating Food Icons */}
      <motion.div
        animate={{
          y: [0, -10, 0],
          rotate: [0, 5, 0]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute top-20 left-10 text-6xl opacity-20"
      >
        🍕
      </motion.div>

      <motion.div
        animate={{
          y: [0, 10, 0],
          rotate: [0, -5, 0]
        }}
        transition={{
          duration: 2.5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5
        }}
        className="absolute top-32 right-16 text-5xl opacity-20"
      >
        🍔
      </motion.div>

      <motion.div
        animate={{
          y: [0, -15, 0],
          rotate: [0, 3, 0]
        }}
        transition={{
          duration: 3.5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
        className="absolute bottom-32 left-20 text-4xl opacity-20"
      >
        🍜
      </motion.div>

      <motion.div
        animate={{
          y: [0, 8, 0],
          rotate: [0, -3, 0]
        }}
        transition={{
          duration: 2.8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1.5
        }}
        className="absolute bottom-40 right-12 text-5xl opacity-20"
      >
        🥗
      </motion.div>
    </div>
  )
}

export default SplashScreen

