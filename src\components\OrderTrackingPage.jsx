import { useParams, useNavigate } from 'react-router-dom'
import { ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'

const OrderTrackingPage = () => {
  const { orderId } = useParams()
  const navigate = useNavigate()
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-md mx-auto">
        <div className="bg-white shadow-sm p-4">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate(-1)}
            >
              <ArrowRight className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-bold">تتبع الطلب</h1>
            <div></div>
          </div>
        </div>
        
        <div className="p-4">
          <p className="text-center text-gray-600 mt-20">
            صفحة تتبع الطلب قيد التطوير...
          </p>
        </div>
      </div>
    </div>
  )
}

export default OrderTrackingPage

