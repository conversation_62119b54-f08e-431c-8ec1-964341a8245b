import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Search, MapPin, Bell, ShoppingCart, Star, Clock, Flame } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const HomePage = ({ user }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [currentTime, setCurrentTime] = useState(new Date())
  const [cartItems, setCartItems] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  const getTimeBasedGreeting = () => {
    const hour = currentTime.getHours()
    if (hour < 12) return 'صباح الخير'
    if (hour < 17) return 'مساء الخير'
    return 'مساء الخير'
  }

  const getTimeBasedSuggestion = () => {
    const hour = currentTime.getHours()
    if (hour < 11) return 'وقت الفطار المثالي!'
    if (hour < 15) return 'وقت الغداء!'
    if (hour < 18) return 'وقت العصرونية!'
    return 'وقت العشاء!'
  }

  const categories = [
    { id: 1, name: 'برجر', nameEn: 'Burgers', icon: '🍔', count: 23, discount: 15 },
    { id: 2, name: 'بيتزا', nameEn: 'Pizza', icon: '🍕', count: 18, discount: 20 },
    { id: 3, name: 'أكل عربي', nameEn: 'Arabic Food', icon: '🥙', count: 31, discount: 10 },
    { id: 4, name: 'صحي', nameEn: 'Healthy', icon: '🥗', count: 15, discount: 0 },
    { id: 5, name: 'حلويات', nameEn: 'Desserts', icon: '🍰', count: 12, discount: 25 },
    { id: 6, name: 'مشروبات', nameEn: 'Drinks', icon: '🥤', count: 8, discount: 0 }
  ]

  const flashOffers = [
    {
      id: 1,
      title: 'عرض البرجر الخارق',
      description: 'اطلب 2 برجر واحصل على الثالث مجاناً',
      image: '/src/assets/images/products/burger1.jpg',
      originalPrice: 45,
      discountPrice: 30,
      timeLeft: '02:45:30',
      isFlash: true
    },
    {
      id: 2,
      title: 'بيتزا العائلة',
      description: 'بيتزا كبيرة + مشروبات + حلى',
      image: '/src/assets/images/products/pizza1.jpg',
      originalPrice: 85,
      discountPrice: 65,
      timeLeft: '01:20:15',
      isFlash: true
    }
  ]

  const featuredProducts = [
    {
      id: 1,
      name: 'برجر كلاسيك',
      description: 'برجر لحم طازج مع الخضار والصوص الخاص',
      image: '/src/assets/images/products/burger2.jpg',
      price: 25,
      rating: 4.8,
      reviews: 156,
      category: 'برجر',
      isPopular: true
    },
    {
      id: 2,
      name: 'بيتزا مارجريتا',
      description: 'بيتزا كلاسيكية بالجبن والطماطم والريحان',
      image: '/src/assets/images/products/pizza2.jpg',
      price: 35,
      rating: 4.9,
      reviews: 203,
      category: 'بيتزا',
      isPopular: true
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-orange-600 font-semibold">
                  {user?.fullName?.charAt(0) || 'م'}
                </span>
              </div>
              <div>
                <p className="text-sm text-gray-600">{getTimeBasedGreeting()}</p>
                <p className="font-semibold text-gray-800">{user?.fullName || 'مستخدم'}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs bg-red-500">
                  3
                </Badge>
              </Button>
              <Button variant="ghost" size="sm" className="relative">
                <ShoppingCart className="h-5 w-5" />
                {cartItems > 0 && (
                  <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs bg-orange-500">
                    {cartItems}
                  </Badge>
                )}
              </Button>
            </div>
          </div>

          {/* Location */}
          <div className="flex items-center text-sm text-gray-600 mb-3">
            <MapPin className="h-4 w-4 ml-1" />
            <span>التوصيل إلى: المعادي، القاهرة</span>
          </div>

          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder="ابحث عن طعامك المفضل..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-10 text-right"
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-md mx-auto px-4 pb-20">
        {/* Time-based Suggestion */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-orange-500 to-yellow-500 rounded-xl p-4 mb-6 mt-4"
        >
          <div className="flex items-center justify-between text-white">
            <div>
              <p className="text-sm opacity-90">الآن</p>
              <p className="font-bold text-lg">{getTimeBasedSuggestion()}</p>
            </div>
            <Clock className="h-8 w-8 opacity-80" />
          </div>
        </motion.div>

        {/* Flash Offers */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-xl font-bold text-gray-800 flex items-center">
              <Flame className="h-5 w-5 text-red-500 ml-2" />
              عروض سريعة
            </h2>
            <Button variant="ghost" size="sm" className="text-orange-600">
              عرض الكل
            </Button>
          </div>
          
          <div className="space-y-3">
            {flashOffers.map((offer) => (
              <motion.div
                key={offer.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                whileHover={{ scale: 1.02 }}
                className="bg-white rounded-xl shadow-sm overflow-hidden"
              >
                <div className="flex">
                  <img
                    src={offer.image}
                    alt={offer.title}
                    className="w-24 h-24 object-cover"
                  />
                  <div className="flex-1 p-3">
                    <div className="flex items-start justify-between mb-1">
                      <h3 className="font-semibold text-gray-800 text-sm">{offer.title}</h3>
                      <Badge className="bg-red-500 text-white text-xs">
                        {offer.timeLeft}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">{offer.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="font-bold text-orange-600">{offer.discountPrice} ج.م</span>
                        <span className="text-xs text-gray-500 line-through">{offer.originalPrice} ج.م</span>
                      </div>
                      <Button size="sm" className="bg-orange-500 hover:bg-orange-600 text-xs px-3 py-1">
                        اطلب الآن
                      </Button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Categories */}
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-3">الأقسام</h2>
          <div className="grid grid-cols-3 gap-3">
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white rounded-xl p-4 shadow-sm text-center cursor-pointer"
              >
                <div className="text-3xl mb-2">{category.icon}</div>
                <h3 className="font-semibold text-gray-800 text-sm mb-1">{category.name}</h3>
                <p className="text-xs text-gray-600">{category.count} منتج</p>
                {category.discount > 0 && (
                  <Badge className="mt-1 bg-green-100 text-green-800 text-xs">
                    خصم {category.discount}%
                  </Badge>
                )}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Featured Products */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-xl font-bold text-gray-800">الأكثر طلباً</h2>
            <Button variant="ghost" size="sm" className="text-orange-600">
              عرض الكل
            </Button>
          </div>
          
          <div className="space-y-4">
            {featuredProducts.map((product) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ scale: 1.02 }}
                className="bg-white rounded-xl shadow-sm overflow-hidden"
              >
                <div className="flex">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-28 h-28 object-cover"
                  />
                  <div className="flex-1 p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="font-semibold text-gray-800">{product.name}</h3>
                        <p className="text-sm text-gray-600 mb-2">{product.description}</p>
                      </div>
                      {product.isPopular && (
                        <Badge className="bg-orange-100 text-orange-800 text-xs">
                          الأكثر طلباً
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-sm font-medium mr-1">{product.rating}</span>
                          <span className="text-xs text-gray-500">({product.reviews})</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <span className="font-bold text-orange-600">{product.price} ج.م</span>
                        <Button 
                          size="sm" 
                          className="bg-orange-500 hover:bg-orange-600"
                          onClick={() => setCartItems(cartItems + 1)}
                        >
                          أضف
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="max-w-md mx-auto px-4 py-2">
          <div className="flex items-center justify-around">
            <Button variant="ghost" className="flex flex-col items-center py-2 text-orange-600">
              <div className="w-6 h-6 mb-1">🏠</div>
              <span className="text-xs">الرئيسية</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center py-2 text-gray-600">
              <div className="w-6 h-6 mb-1">🔍</div>
              <span className="text-xs">البحث</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center py-2 text-gray-600">
              <div className="w-6 h-6 mb-1">📋</div>
              <span className="text-xs">طلباتي</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center py-2 text-gray-600">
              <div className="w-6 h-6 mb-1">👤</div>
              <span className="text-xs">الملف الشخصي</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage

