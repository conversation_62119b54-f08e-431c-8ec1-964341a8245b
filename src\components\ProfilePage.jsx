import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  ArrowRight, 
  Edit, 
  MapPin, 
  CreditCard, 
  Bell, 
  Heart, 
  Settings, 
  HelpCircle, 
  LogOut,
  User,
  Phone,
  Mail,
  Gift,
  Star,
  Shield,
  Moon,
  Globe
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'

const ProfilePage = ({ user, setUser }) => {
  const navigate = useNavigate()
  const [darkMode, setDarkMode] = useState(false)
  const [notifications, setNotifications] = useState(true)
  const [locationSharing, setLocationSharing] = useState(true)

  // Mock user data
  const userData = user || {
    fullName: 'أحمد محمد',
    email: '<EMAIL>',
    phone: '+20 ************',
    avatar: null,
    joinDate: '2023-06-15',
    totalOrders: 24,
    favoriteRestaurants: 8,
    loyaltyPoints: 450,
    membershipLevel: 'ذهبي'
  }

  const stats = [
    {
      icon: '📦',
      label: 'إجمالي الطلبات',
      value: userData.totalOrders,
      color: 'text-blue-600'
    },
    {
      icon: '❤️',
      label: 'المطاعم المفضلة',
      value: userData.favoriteRestaurants,
      color: 'text-red-600'
    },
    {
      icon: '⭐',
      label: 'نقاط الولاء',
      value: userData.loyaltyPoints,
      color: 'text-yellow-600'
    }
  ]

  const menuItems = [
    {
      icon: User,
      label: 'تعديل الملف الشخصي',
      description: 'تحديث معلوماتك الشخصية',
      action: () => navigate('/profile/edit'),
      showArrow: true
    },
    {
      icon: MapPin,
      label: 'عناويني',
      description: 'إدارة عناوين التوصيل',
      action: () => navigate('/addresses'),
      showArrow: true,
      badge: '3'
    },
    {
      icon: CreditCard,
      label: 'طرق الدفع',
      description: 'إدارة البطاقات والمحافظ الرقمية',
      action: () => navigate('/payment-methods'),
      showArrow: true,
      badge: '2'
    },
    {
      icon: Heart,
      label: 'المفضلة',
      description: 'المطاعم والأطباق المفضلة',
      action: () => navigate('/favorites'),
      showArrow: true
    },
    {
      icon: Gift,
      label: 'الكوبونات والعروض',
      description: 'كوبونات الخصم المتاحة',
      action: () => navigate('/coupons'),
      showArrow: true,
      badge: '5'
    }
  ]

  const settingsItems = [
    {
      icon: Bell,
      label: 'الإشعارات',
      description: 'إشعارات الطلبات والعروض',
      type: 'switch',
      value: notifications,
      onChange: setNotifications
    },
    {
      icon: MapPin,
      label: 'مشاركة الموقع',
      description: 'للحصول على توصيات أفضل',
      type: 'switch',
      value: locationSharing,
      onChange: setLocationSharing
    },
    {
      icon: Moon,
      label: 'الوضع الليلي',
      description: 'تفعيل المظهر الداكن',
      type: 'switch',
      value: darkMode,
      onChange: setDarkMode
    },
    {
      icon: Globe,
      label: 'اللغة',
      description: 'العربية',
      action: () => {},
      showArrow: true
    }
  ]

  const supportItems = [
    {
      icon: HelpCircle,
      label: 'مركز المساعدة',
      description: 'الأسئلة الشائعة والدعم',
      action: () => navigate('/help'),
      showArrow: true
    },
    {
      icon: Shield,
      label: 'الخصوصية والأمان',
      description: 'إعدادات الحماية',
      action: () => navigate('/privacy'),
      showArrow: true
    }
  ]

  const handleLogout = () => {
    setUser(null)
    navigate('/')
  }

  const getMembershipColor = (level) => {
    switch (level) {
      case 'ذهبي': return 'bg-yellow-500'
      case 'فضي': return 'bg-gray-400'
      case 'برونزي': return 'bg-orange-600'
      default: return 'bg-blue-500'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate(-1)}
            >
              <ArrowRight className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-bold">الملف الشخصي</h1>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate('/profile/edit')}
            >
              <Edit className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto px-4 pb-20">
        {/* Profile Header */}
        <Card className="mt-4">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4 space-x-reverse mb-4">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                {userData.avatar ? (
                  <img 
                    src={userData.avatar} 
                    alt={userData.fullName}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-orange-600 font-bold text-xl">
                    {userData.fullName.charAt(0)}
                  </span>
                )}
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-gray-800">{userData.fullName}</h2>
                <div className="flex items-center space-x-2 space-x-reverse mt-1">
                  <Badge className={`${getMembershipColor(userData.membershipLevel)} text-white text-xs`}>
                    {userData.membershipLevel}
                  </Badge>
                  <span className="text-sm text-gray-600">
                    عضو منذ {new Date(userData.joinDate).getFullYear()}
                  </span>
                </div>
                <div className="flex items-center mt-2 text-sm text-gray-600">
                  <Phone className="h-4 w-4 ml-1" />
                  {userData.phone}
                </div>
                <div className="flex items-center mt-1 text-sm text-gray-600">
                  <Mail className="h-4 w-4 ml-1" />
                  {userData.email}
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-2xl mb-1">{stat.icon}</div>
                  <div className={`text-lg font-bold ${stat.color}`}>{stat.value}</div>
                  <div className="text-xs text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Account Section */}
        <Card className="mt-4">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-800 mb-4">الحساب</h3>
            <div className="space-y-1">
              {menuItems.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  onClick={item.action}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      <item.icon className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">{item.label}</p>
                      <p className="text-sm text-gray-600">{item.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {item.badge && (
                      <Badge className="bg-orange-500 text-white text-xs">
                        {item.badge}
                      </Badge>
                    )}
                    {item.showArrow && (
                      <ArrowRight className="h-4 w-4 text-gray-400 rotate-180" />
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Settings Section */}
        <Card className="mt-4">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-800 mb-4">الإعدادات</h3>
            <div className="space-y-1">
              {settingsItems.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  onClick={item.action}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      <item.icon className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">{item.label}</p>
                      <p className="text-sm text-gray-600">{item.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {item.type === 'switch' ? (
                      <Switch
                        checked={item.value}
                        onCheckedChange={item.onChange}
                      />
                    ) : item.showArrow ? (
                      <ArrowRight className="h-4 w-4 text-gray-400 rotate-180" />
                    ) : null}
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Support Section */}
        <Card className="mt-4">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-800 mb-4">الدعم</h3>
            <div className="space-y-1">
              {supportItems.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  onClick={item.action}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      <item.icon className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">{item.label}</p>
                      <p className="text-sm text-gray-600">{item.description}</p>
                    </div>
                  </div>
                  {item.showArrow && (
                    <ArrowRight className="h-4 w-4 text-gray-400 rotate-180" />
                  )}
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Logout */}
        <Card className="mt-4">
          <CardContent className="p-4">
            <Button
              onClick={handleLogout}
              variant="ghost"
              className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <LogOut className="h-5 w-5 ml-2" />
              تسجيل الخروج
            </Button>
          </CardContent>
        </Card>

        {/* App Version */}
        <div className="text-center mt-6 mb-4">
          <p className="text-sm text-gray-500">FoodMood v1.0.0</p>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="max-w-md mx-auto px-4 py-2">
          <div className="flex items-center justify-around">
            <Button 
              variant="ghost" 
              className="flex flex-col items-center py-2 text-gray-600"
              onClick={() => navigate('/home')}
            >
              <div className="w-6 h-6 mb-1">🏠</div>
              <span className="text-xs">الرئيسية</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center py-2 text-gray-600">
              <div className="w-6 h-6 mb-1">🔍</div>
              <span className="text-xs">البحث</span>
            </Button>
            <Button 
              variant="ghost" 
              className="flex flex-col items-center py-2 text-gray-600"
              onClick={() => navigate('/orders')}
            >
              <div className="w-6 h-6 mb-1">📋</div>
              <span className="text-xs">طلباتي</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center py-2 text-orange-600">
              <div className="w-6 h-6 mb-1">👤</div>
              <span className="text-xs">الملف الشخصي</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfilePage

