import { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { ArrowRight, Star, Clock, Heart, Share2, Plus, Minus, ShoppingCart } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'

const ProductPage = () => {
  const { productId } = useParams()
  const navigate = useNavigate()
  const [quantity, setQuantity] = useState(1)
  const [selectedSize, setSelectedSize] = useState('medium')
  const [selectedAddons, setSelectedAddons] = useState(new Set())
  const [isFavorite, setIsFavorite] = useState(false)

  // Mock product data
  const product = {
    id: productId,
    name: 'برجر كلاسيك',
    description: 'برجر لحم طازج مشوي على الفحم مع الخضار الطازجة والصوص الخاص بنا. يقدم مع البطاطس المقلية والمخلل.',
    longDescription: 'برجر كلاسيك مميز مصنوع من أجود أنواع اللحم البقري الطازج، مشوي على الفحم للحصول على نكهة مدخنة رائعة. يحتوي على خس طازج، طماطم، بصل، مخلل، وصوصنا الخاص المميز. يقدم في خبز برجر طازج ومحمص.',
    image: '/src/assets/images/products/burger1.jpg',
    images: [
      '/src/assets/images/products/burger1.jpg',
      '/src/assets/images/products/burger2.jpg'
    ],
    price: 25,
    originalPrice: 30,
    rating: 4.8,
    reviews: 156,
    prepTime: '15-20 دقيقة',
    restaurant: 'برجر هاوس',
    category: 'برجر',
    hasDiscount: true,
    isPopular: true,
    calories: 650,
    ingredients: ['لحم بقري', 'خبز برجر', 'خس', 'طماطم', 'بصل', 'مخلل', 'صوص خاص']
  }

  const sizes = [
    { id: 'small', name: 'صغير', price: 0, description: '150 جرام' },
    { id: 'medium', name: 'متوسط', price: 0, description: '200 جرام' },
    { id: 'large', name: 'كبير', price: 5, description: '250 جرام' }
  ]

  const addons = [
    { id: 'cheese', name: 'جبنة إضافية', price: 3 },
    { id: 'bacon', name: 'بيكون', price: 5 },
    { id: 'mushroom', name: 'مشروم', price: 4 },
    { id: 'avocado', name: 'أفوكادو', price: 6 },
    { id: 'fries', name: 'بطاطس مقلية', price: 8 },
    { id: 'drink', name: 'مشروب غازي', price: 5 }
  ]

  const reviews = [
    {
      id: 1,
      user: 'أحمد محمد',
      rating: 5,
      comment: 'برجر رائع! اللحم طازج والطعم ممتاز',
      date: '2024-01-15',
      helpful: 12
    },
    {
      id: 2,
      user: 'سارة أحمد',
      rating: 4,
      comment: 'جيد جداً، لكن التوصيل كان متأخر قليلاً',
      date: '2024-01-10',
      helpful: 8
    },
    {
      id: 3,
      user: 'محمد علي',
      rating: 5,
      comment: 'أفضل برجر جربته! سأطلبه مرة أخرى بالتأكيد',
      date: '2024-01-08',
      helpful: 15
    }
  ]

  const toggleAddon = (addonId) => {
    const newAddons = new Set(selectedAddons)
    if (newAddons.has(addonId)) {
      newAddons.delete(addonId)
    } else {
      newAddons.add(addonId)
    }
    setSelectedAddons(newAddons)
  }

  const calculateTotalPrice = () => {
    let total = product.price
    
    // Add size price
    const size = sizes.find(s => s.id === selectedSize)
    if (size) total += size.price
    
    // Add addons price
    selectedAddons.forEach(addonId => {
      const addon = addons.find(a => a.id === addonId)
      if (addon) total += addon.price
    })
    
    return total * quantity
  }

  const addToCart = () => {
    // Add to cart logic here
    console.log('Added to cart:', {
      product: product.id,
      quantity,
      size: selectedSize,
      addons: Array.from(selectedAddons),
      totalPrice: calculateTotalPrice()
    })
    
    // Show success message or navigate to cart
    navigate('/cart')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate(-1)}
            >
              <ArrowRight className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-bold">تفاصيل المنتج</h1>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFavorite(!isFavorite)}
              >
                <Heart 
                  className={`h-5 w-5 ${
                    isFavorite ? 'text-red-500 fill-current' : 'text-gray-400'
                  }`} 
                />
              </Button>
              <Button variant="ghost" size="sm">
                <Share2 className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-md mx-auto">
        {/* Product Image */}
        <div className="relative">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-64 object-cover"
          />
          {product.hasDiscount && (
            <Badge className="absolute top-4 left-4 bg-red-500 text-white">
              خصم {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
            </Badge>
          )}
          {product.isPopular && (
            <Badge className="absolute top-4 right-4 bg-orange-500 text-white">
              الأكثر طلباً
            </Badge>
          )}
        </div>

        <div className="px-4 pb-32">
          {/* Product Info */}
          <Card className="mt-4">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h1 className="text-xl font-bold text-gray-800 mb-1">{product.name}</h1>
                  <p className="text-sm text-gray-600 mb-2">{product.restaurant}</p>
                  <p className="text-gray-700 text-sm leading-relaxed">{product.description}</p>
                </div>
              </div>

              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="font-medium mr-1">{product.rating}</span>
                    <span className="text-sm text-gray-500">({product.reviews} تقييم)</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 ml-1" />
                    {product.prepTime}
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-xl font-bold text-orange-600">{product.price} ج.م</span>
                    {product.originalPrice && (
                      <span className="text-sm text-gray-500 line-through">
                        {product.originalPrice} ج.م
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">{product.calories} سعرة حرارية</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Size Selection */}
          <Card className="mt-4">
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-800 mb-3">اختر الحجم</h3>
              <div className="space-y-2">
                {sizes.map((size) => (
                  <motion.div
                    key={size.id}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setSelectedSize(size.id)}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedSize === size.id
                        ? 'border-orange-500 bg-orange-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{size.name}</p>
                        <p className="text-sm text-gray-600">{size.description}</p>
                      </div>
                      <div className="text-right">
                        {size.price > 0 && (
                          <p className="font-medium text-orange-600">+{size.price} ج.م</p>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Addons */}
          <Card className="mt-4">
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-800 mb-3">إضافات (اختيارية)</h3>
              <div className="space-y-2">
                {addons.map((addon) => (
                  <motion.div
                    key={addon.id}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => toggleAddon(addon.id)}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedAddons.has(addon.id)
                        ? 'border-orange-500 bg-orange-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`w-5 h-5 rounded border-2 ml-3 flex items-center justify-center ${
                          selectedAddons.has(addon.id)
                            ? 'border-orange-500 bg-orange-500'
                            : 'border-gray-300'
                        }`}>
                          {selectedAddons.has(addon.id) && (
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          )}
                        </div>
                        <p className="font-medium">{addon.name}</p>
                      </div>
                      <p className="font-medium text-orange-600">+{addon.price} ج.م</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Reviews */}
          <Card className="mt-4">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-gray-800">التقييمات</h3>
                <Button variant="ghost" size="sm" className="text-orange-600">
                  عرض الكل
                </Button>
              </div>
              
              <div className="space-y-4">
                {reviews.slice(0, 2).map((review) => (
                  <div key={review.id} className="border-b border-gray-100 pb-4 last:border-b-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center ml-2">
                          <span className="text-orange-600 font-semibold text-sm">
                            {review.user.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-sm">{review.user}</p>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-3 w-3 ${
                                  i < review.rating
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500">{review.date}</p>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{review.comment}</p>
                    <p className="text-xs text-gray-500">مفيد ({review.helpful})</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Bottom Action Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 space-x-reverse">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <span className="font-semibold text-lg px-3">{quantity}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setQuantity(quantity + 1)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="text-right ml-4">
              <p className="text-sm text-gray-600">الإجمالي</p>
              <p className="text-xl font-bold text-orange-600">{calculateTotalPrice()} ج.م</p>
            </div>
            
            <Button 
              onClick={addToCart}
              className="bg-orange-500 hover:bg-orange-600 flex items-center space-x-2 space-x-reverse px-6"
            >
              <ShoppingCart className="h-4 w-4" />
              <span>أضف للسلة</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductPage

